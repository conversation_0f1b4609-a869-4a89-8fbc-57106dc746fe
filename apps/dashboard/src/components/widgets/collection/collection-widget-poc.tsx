// TODO: remove this file when it's ready to make the CollectionWidget component production-ready
import { safQuery } from "@/client"
import { ErrorResult } from "@/components/common/error-result"
import { Loader } from "@/components/common/loader"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { components } from "@saf/sdk"
import { Liquid } from "liquidjs"
import React from "react"

// Initialize Liquid engine
const engine = new Liquid()

// Utility function to access nested object properties by string path
const getNestedProperty = (obj: any, path: string): any => {
  if (!obj || !path) return obj

  return path.split(".").reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// Utility function to render liquid templates with data
const renderLiquidTemplate = async (template: string, data: any): Promise<string> => {
  try {
    if (!template || typeof template !== "string") {
      return ""
    }

    // If template doesn't contain liquid syntax, return as-is
    if (!template.includes("{{") && !template.includes("{%")) {
      return template
    }

    const result = await engine.parseAndRender(template, data)
    return result || ""
  } catch (error) {
    console.warn("Failed to render template:", template, error)
    return template // Return original template if parsing fails
  }
}

const CollectionItem = ({
  item,
  itemsData,
}: {
  item: any
  itemsData: components["schemas"]["CollectionWidget"]["config"]["itemsData"]
}) => {
  // Example liquid templates that could be used:
  // title: "{{title | upcase}}" - uppercase the title
  // subtitle: "{{subtitle | truncate: 50}}" - truncate subtitle to 50 chars
  // meta: "{{status | capitalize}} - {{price}} {{currency}}" - combine multiple fields
  // image: "{{image}}?size=200x200" - add query parameters to image URL
  const [renderedData, setRenderedData] = React.useState<{
    title: string
    subtitle: string
    meta: string
    image: string
  }>({
    title: "",
    subtitle: "",
    meta: "",
    image: "",
  })

  // Render liquid templates when item or itemsData changes
  React.useEffect(() => {
    const renderTemplates = async () => {
      try {
        const [title, subtitle, meta, image] = await Promise.all([
          renderLiquidTemplate(itemsData.title, item),
          renderLiquidTemplate(itemsData.subtitle, item),
          renderLiquidTemplate(itemsData.meta, item),
          renderLiquidTemplate(itemsData.image, item),
        ])

        setRenderedData({
          title,
          subtitle,
          meta,
          image,
        })
      } catch (error) {
        console.warn("Failed to render collection item templates:", error)
        // Fallback to direct item properties
        setRenderedData({
          title: item.title || "",
          subtitle: item.subtitle || "",
          meta: item.meta || "",
          image: item.image || "",
        })
      }
    }

    renderTemplates()
  }, [item, itemsData])

  return (
    <div>
      {renderedData.image && (
        <div>
          <img
            src={renderedData.image}
            alt={renderedData.title || "Collection item"}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
            }}
          />
        </div>
      )}
      <div>
        {renderedData.meta && <p>{renderedData.meta}</p>}
        {renderedData.title && <h4>{renderedData.title}</h4>}
        {renderedData.subtitle && <p>{renderedData.subtitle}</p>}
      </div>
    </div>
  )
}

export const CollectionWidgetPoc = ({ data }: { data: components["schemas"]["CollectionWidget"] }) => {
  const { config } = data
  const { itemsData, options, pagination, rootArrayPath } = config

  const {
    data: externalApiResponse,
    isLoading,
    error,
    isError,
  } = safQuery.useQuery(
    "post",
    "/api/customers/external-api/{externalApiId}",
    {
      params: {
        path: {
          externalApiId: config.bindingConfig?.externalApiId || 0,
        },
      },
      body: {},
    },
    {
      enabled: config.bindingConfig?.dataSourceId != null && config.bindingConfig?.externalApiId != null,
    },
  )

  if (isLoading) {
    return (
      <div>
        <Loader />
      </div>
    )
  }

  if (isError) {
    return <ErrorResult message={showHumanFriendlyError(error)} />
  }

  if (!itemsData?.title && !itemsData?.subtitle && !itemsData?.meta && !itemsData?.image) {
    return <div>No collection configuration</div>
  }

  // Extract items from external API response using rootArrayPath if specified
  const extractItemsFromResponse = (responseData: any): any[] => {
    if (!responseData) return []

    // If rootArrayPath is specified, use it to navigate to the correct array
    if (rootArrayPath) {
      const nestedData = getNestedProperty(responseData, rootArrayPath)
      return Array.isArray(nestedData) ? nestedData : []
    }

    // Default behavior: use the data directly
    return Array.isArray(responseData) ? responseData : [responseData]
  }

  const items = extractItemsFromResponse(externalApiResponse?.data)

  // Apply limit if specified
  const limitedItems = options?.limitItems ? items.slice(0, options.limitItems) : items

  if (!limitedItems || limitedItems.length === 0) {
    return <div>No items to display</div>
  }

  return (
    <div>
      <div>
        {limitedItems.map((item, index) => (
          <CollectionItem key={item.id || index} item={item} itemsData={itemsData} />
        ))}
      </div>

      {pagination?.enabled && (
        <div>
          {pagination.page || "1"} / {pagination.limit || "10"}
        </div>
      )}
    </div>
  )
}
