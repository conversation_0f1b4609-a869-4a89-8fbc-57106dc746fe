/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/admin/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["Auth_login"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/auth/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["Auth_me"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/auth/refresh-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["Auth_refreshToken"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/invitations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["Invitation_listInvitations"];
        put?: never;
        post: operations["Invitation_inviteToSystem"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/invitations/{invitationId}/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["Invitation_cancelInvitation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/invitations/{token}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["Invitation_validateInvitation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/invitations/{token}/accept": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["Invitation_acceptInvitation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/invitations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["Invitation_listTeamInvitations"];
        put?: never;
        post: operations["Invitation_inviteToTeam"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/invitations/{invitationId}/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["Invitation_cancelTeamInvitation"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["MiniApp_listMiniApps"];
        put?: never;
        post: operations["MiniApp_createMiniApp"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["MiniApp_getMiniApp"];
        put?: never;
        post?: never;
        delete: operations["MiniApp_deleteMiniApp"];
        options?: never;
        head?: never;
        patch: operations["MiniApp_updateMiniApp"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DataSource_list"];
        put?: never;
        post: operations["DataSource_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DataSource_get"];
        put?: never;
        post?: never;
        delete: operations["DataSource_delete"];
        options?: never;
        head?: never;
        patch: operations["DataSource_update"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ExternalApi_list"];
        put?: never;
        post: operations["ExternalApi_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ExternalApi_get"];
        put?: never;
        post?: never;
        delete: operations["ExternalApi_delete"];
        options?: never;
        head?: never;
        patch: operations["ExternalApi_update"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/execute": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ExternalApi_execute"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}/external-api-snapshots": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ExternalApi_getSnapshots"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["MiniAppVersion_list"];
        put?: never;
        post: operations["MiniAppVersion_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["MiniAppVersion_get"];
        put?: never;
        post?: never;
        delete: operations["MiniAppVersion_delete"];
        options?: never;
        head?: never;
        patch: operations["MiniAppVersion_update"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["MiniAppPage_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["MiniAppPage_delete"];
        options?: never;
        head?: never;
        patch: operations["MiniAppPage_update"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/move": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch: operations["MiniAppPage_move"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["MiniAppWidget_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["MiniAppWidget_delete"];
        options?: never;
        head?: never;
        patch: operations["MiniAppWidget_update"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}/move": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch: operations["MiniAppWidget_move"];
        trace?: never;
    };
    "/api/admin/teams/{teamId}/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["User_listTeamUsers"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/teams/{teamId}/users/{userId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["User_getTeamUser"];
        put?: never;
        post?: never;
        delete: operations["User_deleteTeamUser"];
        options?: never;
        head?: never;
        patch: operations["User_updateTeamUser"];
        trace?: never;
    };
    "/api/admin/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["User_listUsers"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/admin/users/{userId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["User_getUser"];
        put?: never;
        post?: never;
        delete: operations["User_deleteUser"];
        options?: never;
        head?: never;
        patch: operations["User_updateUser"];
        trace?: never;
    };
    "/api/customers/auth/refresh-token": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["Auth_refreshToken"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/customers/external-api/{externalApiId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["ExternalApiCustomer_execute"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        AcceptInvitationRequest: {
            name: string;
            password: string;
            confirmPassword?: string;
        };
        Action: {
            title: string;
            icon: string;
            actionType: components["schemas"]["ActionType"];
        };
        /** @enum {string} */
        ActionType: "navigate_to_page" | "navigate_back" | "open_url" | "submit_form";
        AlertWidget: {
            /** @enum {string} */
            widgetType: "alert";
            config: {
                title: string;
                subtitle?: string;
                design: {
                    /** @enum {string} */
                    type: "info" | "warning" | "error" | "success";
                };
            };
        } & components["schemas"]["BaseWidget"];
        AlertWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "alert";
            config?: {
                title?: string;
                subtitle?: string;
                design?: {
                    /** @enum {string} */
                    type?: "info" | "warning" | "error" | "success";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        BaseDataSource: {
            /** Format: int32 */
            id: number;
            name: string;
            dataSourceType: components["schemas"]["DataSourceType"];
            description?: string;
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            createdBy: string;
            /** Format: date-time */
            updatedAt?: string;
            /** Format: date-time */
            updatedBy?: string;
        };
        BaseInputWidget: {
            config: {
                label: string;
                required: boolean;
                binding?: string;
            };
        } & components["schemas"]["BaseWidget"];
        BaseInputWidgetUpdate: {
            config?: {
                label?: string;
                required?: boolean;
                binding?: string;
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        BaseUser: {
            /** Format: int64 */
            id: number;
            email: string;
            name?: string;
            userStatus: components["schemas"]["UserStatus"];
            /** Format: date-time */
            createdAt: string;
        };
        BaseUserUpdate: {
            /** Format: int64 */
            id?: number;
            email?: string;
            name?: string;
            userStatus?: components["schemas"]["UserStatus"];
            /** Format: date-time */
            createdAt?: string;
        };
        BaseWidget: {
            /** Format: int32 */
            id: number;
            name: string;
            /** Format: int32 */
            position: number;
            isHidden: boolean;
            actionConfig?: string;
            bindingConfig?: string;
        };
        BaseWidgetUpdate: {
            /** Format: int32 */
            id?: number;
            name?: string;
            /** Format: int32 */
            position?: number;
            isHidden?: boolean;
            actionConfig?: string;
            bindingConfig?: string;
        };
        BigNumberWidget: {
            /** @enum {string} */
            widgetType: "big_number";
            config: {
                fields: {
                    title: string;
                    value?: string;
                }[];
                design?: {
                    /** @enum {string} */
                    size?: "small" | "medium" | "large";
                };
            };
        } & components["schemas"]["BaseWidget"];
        BigNumberWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "big_number";
            config?: {
                fields?: {
                    title: string;
                    value?: string;
                }[];
                design?: {
                    /** @enum {string} */
                    size?: "small" | "medium" | "large";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        BindingConfig: {
            dataSourceId?: number;
            externalApiId?: number;
        };
        ButtonWidget: {
            /** @enum {string} */
            widgetType: "button";
            config: {
                buttons: components["schemas"]["Action"][];
                design?: {
                    /** @enum {string} */
                    primary?: "left" | "none" | "right";
                };
            };
        } & components["schemas"]["BaseWidget"];
        ButtonWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "button";
            config?: {
                buttons?: components["schemas"]["Action"][];
                design?: {
                    /** @enum {string} */
                    primary?: "left" | "none" | "right";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        CheckboxWidget: {
            /** @enum {string} */
            widgetType: "checkbox";
        } & components["schemas"]["BaseInputWidget"];
        CheckboxWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "checkbox";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        ChoiceWidget: {
            /** @enum {string} */
            widgetType: "choice";
        } & components["schemas"]["BaseInputWidget"];
        ChoiceWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "choice";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        CollectionWidget: {
            /** @enum {string} */
            widgetType: "collection";
            config: {
                bindingConfig?: components["schemas"]["BindingConfig"];
                rootArrayPath?: string;
                /** @enum {string} */
                style: "list" | "grid";
                itemsData: {
                    title: string;
                    subtitle: string;
                    meta: string;
                    image: string;
                };
                design: {
                    /** @enum {string} */
                    size: "default" | "compact";
                    /** @enum {string} */
                    style: "default" | "card";
                    /** @enum {string} */
                    imageShape: "circle" | "square";
                };
                options: {
                    /** Format: int32 */
                    limitItems?: number;
                };
                pagination: {
                    enabled: boolean;
                    page?: string;
                    limit?: string;
                };
            };
        } & components["schemas"]["BaseWidget"];
        CollectionWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "collection";
            config?: {
                bindingConfig?: components["schemas"]["BindingConfig"];
                rootArrayPath?: string;
                /** @enum {string} */
                style?: "list" | "grid";
                itemsData?: {
                    title?: string;
                    subtitle?: string;
                    meta?: string;
                    image?: string;
                };
                design?: {
                    /** @enum {string} */
                    size?: "default" | "compact";
                    /** @enum {string} */
                    style?: "default" | "card";
                    /** @enum {string} */
                    imageShape?: "circle" | "square";
                };
                options?: {
                    /** Format: int32 */
                    limitItems?: number;
                };
                pagination?: {
                    enabled?: boolean;
                    page?: string;
                    limit?: string;
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        /** @description The template for omitting properties. */
        CreateExternalApiDataSourceRequest: {
            /** @enum {string} */
            dataSourceType: "external_api";
            config?: components["schemas"]["ExternalApiConfig"];
            name: string;
            description?: string;
        };
        /** @description The template for omitting properties. */
        CreateExternalApiRequest: {
            name: string;
            baseUrl?: string;
            urlPath: string;
            /** @enum {string} */
            httpMethod: "get" | "post" | "put" | "delete" | "patch" | "options" | "head" | "connect" | "trace";
            config: components["schemas"]["ExternalApiRequestConfig"];
        };
        /** @description The template for omitting properties. */
        CreateInternalTableDataSourceRequest: {
            /** @enum {string} */
            dataSourceType: "internal_table";
            config?: components["schemas"]["InternalTableConfig"];
            name: string;
            description?: string;
        };
        /** @description The template for omitting properties. */
        CreateMiniAppPageRequest: {
            /** Format: int32 */
            miniAppVersionId: number;
            name: string;
            title?: string;
            icon?: string;
            isHidden: boolean;
            hideInNavbar: boolean;
            /** Format: int32 */
            position: number;
            widgets?: (components["schemas"]["FormContainerWidget"] | components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"])[];
        };
        /** @description The template for omitting properties. */
        CreateMiniAppRequest: {
            name: string;
            description?: string;
            customerServiceContactNumber?: string;
            customerServiceContactEmail?: string;
            termsAndConditionsUrl?: string;
            /** Format: int32 */
            categoryId?: number;
        };
        /** @description The template for omitting properties. */
        CreateMiniAppVersionRequest: {
            version: string;
            miniAppType: components["schemas"]["MiniAppType"];
            releaseNote?: string;
            miniAppUrl?: string;
            url?: string;
            thumbnailUrl?: string;
        };
        CreateMiniAppWidgetRequest: {
            name: string;
            /** Format: int32 */
            position: number;
            isHidden: boolean;
            actionConfig?: string;
            bindingConfig?: string;
            widgetType: components["schemas"]["WidgetType"];
            config: unknown;
        };
        /** @enum {string} */
        DataSourceType: "internal_table" | "external_api";
        DateTimeWidget: {
            /** @enum {string} */
            widgetType: "date_time";
        } & components["schemas"]["BaseInputWidget"];
        DateTimeWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "date_time";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        DateWidget: {
            /** @enum {string} */
            widgetType: "date";
        } & components["schemas"]["BaseInputWidget"];
        DateWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "date";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        EmailEntryWidget: {
            /** @enum {string} */
            widgetType: "email_entry";
        } & components["schemas"]["BaseInputWidget"];
        EmailEntryWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "email_entry";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        ExternalApiConfig: {
            name: string;
            baseUrl?: string;
        } & components["schemas"]["ExternalApiRequestConfig"];
        ExternalApiConfigUpdate: {
            name?: string;
            baseUrl?: string;
        } & components["schemas"]["ExternalApiRequestConfig"];
        ExternalApiDataSource: {
            /** @enum {string} */
            dataSourceType: "external_api";
            config?: components["schemas"]["ExternalApiConfig"];
        } & WithRequired<components["schemas"]["BaseDataSource"], "dataSourceType">;
        ExternalApiRequestConfig: {
            routeParameters?: {
                [key: string]: string;
            };
            headers?: {
                [key: string]: string;
            };
            queryParameters?: {
                [key: string]: string;
            };
            body?: {
                [key: string]: unknown;
            };
            cookies?: {
                [key: string]: string;
            };
        };
        ExternalApiResponse: {
            /** Format: int32 */
            id: number;
            name: string;
            baseUrl?: string;
            urlPath: string;
            /** @enum {string} */
            httpMethod: "get" | "post" | "put" | "delete" | "patch" | "options" | "head" | "connect" | "trace";
            config: components["schemas"]["ExternalApiRequestConfig"];
        };
        ExternalApiResponseSnapshot: {
            isSuccess: boolean;
            /** Format: int32 */
            statusCode: number;
            data: unknown;
            schema: unknown;
        };
        FieldsWidget: {
            /** @enum {string} */
            widgetType: "fields";
            config: {
                fields: {
                    title: string;
                    subtitle?: string;
                    meta?: string;
                    image?: string;
                }[];
                design: {
                    /** @enum {string} */
                    style: "default" | "compact";
                    /** @enum {string} */
                    imageShape: "circle" | "square";
                };
            };
        } & components["schemas"]["BaseWidget"];
        FieldsWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "fields";
            config?: {
                fields?: {
                    title: string;
                    subtitle?: string;
                    meta?: string;
                    image?: string;
                }[];
                design?: {
                    /** @enum {string} */
                    style?: "default" | "compact";
                    /** @enum {string} */
                    imageShape?: "circle" | "square";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        FilePickerWidget: {
            /** @enum {string} */
            widgetType: "file_picker";
        } & components["schemas"]["BaseInputWidget"];
        FilePickerWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "file_picker";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        FormContainerWidget: {
            /** @enum {string} */
            widgetType: "form_container";
            config: {
                title?: string;
                subtitle?: string;
                bindingConfig?: components["schemas"]["BindingConfig"];
                widgets: (components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"])[];
                design?: {
                    submitButtonText?: string;
                };
            };
        } & components["schemas"]["BaseWidget"];
        FormContainerWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "form_container";
            config?: {
                title?: string;
                subtitle?: string;
                bindingConfig?: components["schemas"]["BindingConfig"];
                widgets?: (components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"])[];
                design?: {
                    submitButtonText?: string;
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        GenericError: {
            type: string;
            statusCode: number;
            /** @default An error occurred */
            message: string;
        };
        ImagePickerWidget: {
            /** @enum {string} */
            widgetType: "image_picker";
        } & components["schemas"]["BaseInputWidget"];
        ImagePickerWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "image_picker";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        ImageWidget: {
            /** @enum {string} */
            widgetType: "image";
            config: {
                content: string;
                design: {
                    /** @enum {string} */
                    aspectRatio: "1:1" | "16:9" | "4:3" | "3:2" | "auto";
                    /** @enum {string} */
                    fill: "fill" | "contain";
                    fullWidth?: boolean;
                };
            };
        } & components["schemas"]["BaseWidget"];
        ImageWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "image";
            config?: {
                content?: string;
                design?: {
                    /** @enum {string} */
                    aspectRatio?: "1:1" | "16:9" | "4:3" | "3:2" | "auto";
                    /** @enum {string} */
                    fill?: "fill" | "contain";
                    fullWidth?: boolean;
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        InternalTableConfig: {
            permissions: components["schemas"]["InternalTablePermissions"];
            schema: components["schemas"]["InternalTableSchema"][];
        };
        InternalTableConfigUpdate: {
            permissions?: components["schemas"]["InternalTablePermissionsUpdate"];
            schema?: components["schemas"]["InternalTableSchema"][];
        };
        InternalTableDataSource: {
            /** @enum {string} */
            dataSourceType: "internal_table";
            config?: components["schemas"]["InternalTableConfig"];
        } & WithRequired<components["schemas"]["BaseDataSource"], "dataSourceType">;
        /** @enum {string} */
        InternalTableFieldType: "string" | "number" | "boolean" | "datetime" | "image";
        /** @enum {string} */
        InternalTablePermissionType: "allow" | "deny" | "owner";
        InternalTablePermissions: {
            create: components["schemas"]["InternalTablePermissionType"];
            read: components["schemas"]["InternalTablePermissionType"];
            update: components["schemas"]["InternalTablePermissionType"];
            delete: components["schemas"]["InternalTablePermissionType"];
        };
        InternalTablePermissionsUpdate: {
            create?: components["schemas"]["InternalTablePermissionType"];
            read?: components["schemas"]["InternalTablePermissionType"];
            update?: components["schemas"]["InternalTablePermissionType"];
            delete?: components["schemas"]["InternalTablePermissionType"];
        };
        InternalTableSchema: {
            name: string;
            type: components["schemas"]["InternalTableFieldType"];
            required: boolean;
        };
        InvitationResponse: {
            /** Format: int64 */
            id: number;
            email: string;
            invitationStatus: components["schemas"]["InvitationStatus"];
            systemRoles?: components["schemas"]["SystemRole"][];
            teamAssignments?: {
                /** Format: int32 */
                teamId: number;
                teamName: string;
                teamRoles: components["schemas"]["TeamRole"][];
            }[];
            token: string;
            invitedBy: {
                /** Format: int32 */
                id: number;
                name: string;
                email: string;
            };
            expiredAt: string;
            createdAt: string;
        };
        /** @enum {string} */
        InvitationStatus: "pending" | "accepted" | "cancelled";
        InviteToSystemRequest: {
            email: string;
            systemRoles: string[];
        };
        InviteToTeamRequest: {
            email: string;
            roles: string[];
        };
        LinkWidget: {
            /** @enum {string} */
            widgetType: "link";
            config: {
                links: components["schemas"]["Action"][];
                design?: {
                    /** @enum {string} */
                    size?: "left" | "none" | "right";
                };
            };
        } & components["schemas"]["BaseWidget"];
        LinkWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "link";
            config?: {
                links?: components["schemas"]["Action"][];
                design?: {
                    /** @enum {string} */
                    size?: "left" | "none" | "right";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        MiniApp: {
            /** Format: int32 */
            id: number;
            name: string;
            description?: string;
            customerServiceContactNumber?: string;
            customerServiceContactEmail?: string;
            termsAndConditionsUrl?: string;
            miniAppStatus: components["schemas"]["MiniAppStatus"];
            /** Format: int32 */
            categoryId?: number;
            /** Format: int32 */
            teamId: number;
            createdAt: string;
            createdBy: string;
            updatedAt?: string;
            updatedBy?: string;
        };
        MiniAppPage: {
            /** Format: int32 */
            id: number;
            /** Format: int32 */
            miniAppVersionId: number;
            name: string;
            title?: string;
            icon?: string;
            isHidden: boolean;
            hideInNavbar: boolean;
            /** Format: int32 */
            position: number;
            widgets?: (components["schemas"]["FormContainerWidget"] | components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"])[];
        };
        /** @enum {string} */
        MiniAppStatus: "removed_by_admin" | "active";
        /** @enum {string} */
        MiniAppType: "url" | "bundle" | "builder";
        MiniAppVersion: {
            /** Format: int32 */
            id: number;
            version: string;
            miniAppType: components["schemas"]["MiniAppType"];
            miniAppVersionStatus: components["schemas"]["MiniAppVersionStatus"];
            releaseNote?: string;
            miniAppUrl?: string;
            url?: string;
            thumbnailUrl?: string;
            s3Key?: string;
            createdAt: string;
            createdBy: string;
            updatedAt?: string;
            updatedBy?: string;
        };
        MiniAppVersionDetail: {
            pages: components["schemas"]["MiniAppPage"][];
        } & components["schemas"]["MiniAppVersion"];
        /** @enum {string} */
        MiniAppVersionStatus: "draft" | "in_review" | "approved" | "live" | "rejected";
        MoveRequest: {
            /** Format: int32 */
            targetPosition?: number;
        };
        NumberEntryWidget: {
            /** @enum {string} */
            widgetType: "number_entry";
        } & components["schemas"]["BaseInputWidget"];
        NumberEntryWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "number_entry";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        PhoneEntryWidget: {
            /** @enum {string} */
            widgetType: "phone_entry";
        } & components["schemas"]["BaseInputWidget"];
        PhoneEntryWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "phone_entry";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        RefreshTokenRequest: {
            userId: string;
            refreshToken: string;
        };
        RefreshTokenResponse: {
            refreshToken: string;
            accessToken: string;
        };
        RichTextWidget: {
            /** @enum {string} */
            widgetType: "rich_text";
            config: {
                content: string;
            };
        } & components["schemas"]["BaseWidget"];
        RichTextWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "rich_text";
            config?: {
                content?: string;
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        SeparatorWidget: {
            /** @enum {string} */
            widgetType: "separator";
            config: {
                design: {
                    /** @enum {string} */
                    size: "small" | "medium" | "large";
                    drawLine?: boolean;
                };
            };
        } & components["schemas"]["BaseWidget"];
        SeparatorWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "separator";
            config?: {
                design?: {
                    /** @enum {string} */
                    size?: "small" | "medium" | "large";
                    drawLine?: boolean;
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        /** @enum {string} */
        SystemRole: "system_admin" | "system_editor";
        /** @enum {string} */
        TeamRole: "team_admin" | "team_developer";
        TeamUserResponse: {
            teamRoles?: components["schemas"]["TeamRole"][];
        } & components["schemas"]["BaseUser"];
        TeamUserResponseUpdate: {
            teamRoles?: components["schemas"]["TeamRole"][];
        } & components["schemas"]["BaseUserUpdate"];
        TextEntryWidget: {
            /** @enum {string} */
            widgetType: "text_entry";
        } & components["schemas"]["BaseInputWidget"];
        TextEntryWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "text_entry";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        TextWidget: {
            /** @enum {string} */
            widgetType: "text";
            config: {
                content: string;
                design: {
                    /** @enum {string} */
                    style: "large" | "regular" | "small" | "footnote" | "meta_text" | "headline_xsmall" | "headline_small" | "headline_medium" | "headline_large" | "headline_xlarge";
                    /** @enum {string} */
                    textAlign: "left" | "center" | "right";
                };
            };
        } & components["schemas"]["BaseWidget"];
        TextWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "text";
            config?: {
                content?: string;
                design?: {
                    /** @enum {string} */
                    style?: "large" | "regular" | "small" | "footnote" | "meta_text" | "headline_xsmall" | "headline_small" | "headline_medium" | "headline_large" | "headline_xlarge";
                    /** @enum {string} */
                    textAlign?: "left" | "center" | "right";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        TimeWidget: {
            /** @enum {string} */
            widgetType: "time";
        } & components["schemas"]["BaseInputWidget"];
        TimeWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "time";
        } & components["schemas"]["BaseInputWidgetUpdate"];
        TitleWidget: {
            /** @enum {string} */
            widgetType: "title";
            config: {
                /** @enum {string} */
                style: "simple" | "banner";
                data: {
                    title?: string;
                    subtitle?: string;
                    meta?: string;
                    image?: string;
                };
                design: {
                    /** @enum {string} */
                    imageFill: "fill" | "contain";
                };
            };
        } & components["schemas"]["BaseWidget"];
        TitleWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "title";
            config?: {
                /** @enum {string} */
                style?: "simple" | "banner";
                data?: {
                    title?: string;
                    subtitle?: string;
                    meta?: string;
                    image?: string;
                };
                design?: {
                    /** @enum {string} */
                    imageFill?: "fill" | "contain";
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        /** @description The template for omitting properties. */
        UpdateExternalApiDataSourceRequest: {
            /** @enum {string} */
            dataSourceType?: "external_api";
            config?: components["schemas"]["ExternalApiConfigUpdate"];
            name?: string;
            description?: string;
        };
        /** @description The template for omitting properties. */
        UpdateExternalApiRequest: {
            name?: string;
            baseUrl?: string;
            urlPath?: string;
            /** @enum {string} */
            httpMethod?: "get" | "post" | "put" | "delete" | "patch" | "options" | "head" | "connect" | "trace";
            config?: components["schemas"]["ExternalApiRequestConfig"];
        };
        /** @description The template for omitting properties. */
        UpdateInternalTableDataSourceRequest: {
            /** @enum {string} */
            dataSourceType?: "internal_table";
            config?: components["schemas"]["InternalTableConfigUpdate"];
            name?: string;
            description?: string;
        };
        /** @description The template for omitting properties. */
        UpdateMiniAppPageRequest: {
            name?: string;
            title?: string;
            icon?: string;
            isHidden?: boolean;
            hideInNavbar?: boolean;
            /** Format: int32 */
            position?: number;
            widgets?: (components["schemas"]["FormContainerWidget"] | components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"])[];
        };
        /** @description The template for omitting properties. */
        UpdateMiniAppRequest: {
            name?: string;
            description?: string;
            customerServiceContactNumber?: string;
            customerServiceContactEmail?: string;
            termsAndConditionsUrl?: string;
            miniAppStatus?: components["schemas"]["MiniAppStatus"];
            /** Format: int32 */
            categoryId?: number;
        };
        /** @description The template for omitting properties. */
        UpdateMiniAppVersionRequest: {
            version?: string;
            miniAppType?: components["schemas"]["MiniAppType"];
            releaseNote?: string;
            miniAppUrl?: string;
            url?: string;
            thumbnailUrl?: string;
        };
        /** @description The template for omitting properties. */
        UpdateUserRequest: {
            systemRoles?: components["schemas"]["SystemRole"][];
            teams?: components["schemas"]["UserTeamRoles"][];
            /** Format: date-time */
            createdAt?: string;
            email?: string;
            name?: string;
            userStatus?: components["schemas"]["UserStatus"];
        };
        UserLoginRequest: {
            email: string;
            password: string;
        };
        UserLoginResponse: {
            user: components["schemas"]["UserResponse"];
            refreshToken: string;
            accessToken: string;
        };
        UserResponse: {
            systemRoles?: components["schemas"]["SystemRole"][];
            teams?: components["schemas"]["UserTeamRoles"][];
            /** Format: date-time */
            createdAt: string;
        } & WithRequired<components["schemas"]["BaseUser"], "createdAt">;
        UserResponseUpdate: {
            systemRoles?: components["schemas"]["SystemRole"][];
            teams?: components["schemas"]["UserTeamRoles"][];
            /** Format: date-time */
            createdAt?: string;
        } & components["schemas"]["BaseUserUpdate"];
        /** @enum {string} */
        UserStatus: "active" | "inactive" | "suspended" | "deleted";
        UserTeamRoles: {
            /** Format: int32 */
            id: number;
            name: string;
            roles: components["schemas"]["TeamRole"][];
        };
        ValidationError: {
            type: string;
            statusCode: number;
            /** @default An error occurred */
            message: string;
            errors: unknown;
        };
        VideoWidget: {
            /** @enum {string} */
            widgetType: "video";
            config: {
                url: string;
                design?: {
                    /** @enum {string} */
                    aspectRatio: "1:1" | "16:9" | "4:3" | "3:2";
                    fullWidth?: boolean;
                };
            };
        } & components["schemas"]["BaseWidget"];
        VideoWidgetUpdate: {
            /** @enum {string} */
            widgetType?: "video";
            config?: {
                url?: string;
                design?: {
                    /** @enum {string} */
                    aspectRatio?: "1:1" | "16:9" | "4:3" | "3:2";
                    fullWidth?: boolean;
                };
            };
        } & components["schemas"]["BaseWidgetUpdate"];
        /** @enum {string} */
        WidgetType: "collection" | "title" | "separator" | "text" | "rich_text" | "alert" | "fields" | "image" | "video" | "big_number" | "button" | "link" | "form_container" | "text_entry" | "date_time" | "date" | "time" | "number_entry" | "phone_entry" | "email_entry" | "checkbox" | "image_picker" | "file_picker" | "choice";
    };
    responses: never;
    parameters: {
        "PaginationParams.limit": string;
        "PaginationParams.page": string;
        "PaginationParams.sort": string;
    };
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    Auth_login: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserLoginRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserLoginResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Auth_me: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Auth_refreshToken: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RefreshTokenRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RefreshTokenResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_listInvitations: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["InvitationResponse"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_inviteToSystem: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InviteToSystemRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_cancelInvitation: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                invitationId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_validateInvitation: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                token: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_acceptInvitation: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                token: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AcceptInvitationRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_listTeamInvitations: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["InvitationResponse"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_inviteToTeam: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InviteToTeamRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Invitation_cancelTeamInvitation: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                invitationId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniApp_listMiniApps: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["MiniApp"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniApp_createMiniApp: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateMiniAppRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniApp"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniApp_getMiniApp: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniApp"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniApp_deleteMiniApp: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniApp_updateMiniApp: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateMiniAppRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniApp"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    DataSource_list: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: (components["schemas"]["InternalTableDataSource"] | components["schemas"]["ExternalApiDataSource"])[];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    DataSource_create: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateExternalApiDataSourceRequest"] | components["schemas"]["CreateInternalTableDataSourceRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InternalTableDataSource"] | components["schemas"]["ExternalApiDataSource"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    DataSource_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InternalTableDataSource"] | components["schemas"]["ExternalApiDataSource"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    DataSource_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    DataSource_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateExternalApiDataSourceRequest"] | components["schemas"]["UpdateInternalTableDataSourceRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["InternalTableDataSource"] | components["schemas"]["ExternalApiDataSource"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_list: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["ExternalApiResponse"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_create: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateExternalApiRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateExternalApiRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_execute: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponseSnapshot"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    ExternalApi_getSnapshots: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                dataSourceId: number;
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponseSnapshot"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppVersion_list: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["MiniAppVersion"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppVersion_create: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateMiniAppVersionRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniAppVersionDetail"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppVersion_get: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniAppVersionDetail"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppVersion_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppVersion_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateMiniAppVersionRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniAppVersionDetail"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppPage_create: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateMiniAppPageRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniAppPage"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppPage_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppPage_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateMiniAppPageRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["MiniAppPage"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppPage_move: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MoveRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppWidget_create: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateMiniAppWidgetRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded and a new resource has been created as a result. */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FormContainerWidget"] | components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppWidget_delete: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
                widgetId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppWidget_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
                widgetId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["FormContainerWidgetUpdate"] | components["schemas"]["CollectionWidgetUpdate"] | components["schemas"]["SeparatorWidgetUpdate"] | components["schemas"]["TitleWidgetUpdate"] | components["schemas"]["TextWidgetUpdate"] | components["schemas"]["RichTextWidgetUpdate"] | components["schemas"]["AlertWidgetUpdate"] | components["schemas"]["FieldsWidgetUpdate"] | components["schemas"]["ImageWidgetUpdate"] | components["schemas"]["VideoWidgetUpdate"] | components["schemas"]["BigNumberWidgetUpdate"] | components["schemas"]["ButtonWidgetUpdate"] | components["schemas"]["LinkWidgetUpdate"] | components["schemas"]["TextEntryWidgetUpdate"] | components["schemas"]["DateTimeWidgetUpdate"] | components["schemas"]["DateWidgetUpdate"] | components["schemas"]["TimeWidgetUpdate"] | components["schemas"]["NumberEntryWidgetUpdate"] | components["schemas"]["PhoneEntryWidgetUpdate"] | components["schemas"]["EmailEntryWidgetUpdate"] | components["schemas"]["CheckboxWidgetUpdate"] | components["schemas"]["ImagePickerWidgetUpdate"] | components["schemas"]["FilePickerWidgetUpdate"] | components["schemas"]["ChoiceWidgetUpdate"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FormContainerWidget"] | components["schemas"]["CollectionWidget"] | components["schemas"]["SeparatorWidget"] | components["schemas"]["TitleWidget"] | components["schemas"]["TextWidget"] | components["schemas"]["RichTextWidget"] | components["schemas"]["AlertWidget"] | components["schemas"]["FieldsWidget"] | components["schemas"]["ImageWidget"] | components["schemas"]["VideoWidget"] | components["schemas"]["BigNumberWidget"] | components["schemas"]["ButtonWidget"] | components["schemas"]["LinkWidget"] | components["schemas"]["TextEntryWidget"] | components["schemas"]["DateTimeWidget"] | components["schemas"]["DateWidget"] | components["schemas"]["TimeWidget"] | components["schemas"]["NumberEntryWidget"] | components["schemas"]["PhoneEntryWidget"] | components["schemas"]["EmailEntryWidget"] | components["schemas"]["CheckboxWidget"] | components["schemas"]["ImagePickerWidget"] | components["schemas"]["FilePickerWidget"] | components["schemas"]["ChoiceWidget"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    MiniAppWidget_move: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                miniAppId: number;
                versionId: number;
                pageId: number;
                widgetId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MoveRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_listTeamUsers: {
        parameters: {
            query?: {
                email?: string;
                name?: string;
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path: {
                teamId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["TeamUserResponse"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_getTeamUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                userId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TeamUserResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_deleteTeamUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                userId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_updateTeamUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                teamId: number;
                userId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TeamUserResponseUpdate"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TeamUserResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_listUsers: {
        parameters: {
            query?: {
                page?: components["parameters"]["PaginationParams.page"];
                limit?: components["parameters"]["PaginationParams.limit"];
                sort?: components["parameters"]["PaginationParams.sort"];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        items: components["schemas"]["UserResponse"][];
                        /** Format: int32 */
                        totalCount: number;
                        /** Format: int32 */
                        page: number;
                        /** Format: int32 */
                        pageSize: number;
                        /** Format: int32 */
                        totalPages: number;
                    };
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_getUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_deleteUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description There is no content to send for this request, but the headers may be useful.  */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    User_updateUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateUserRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserResponse"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
    Auth_refreshToken: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RefreshTokenRequest"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserLoginResponse"];
                };
            };
        };
    };
    ExternalApiCustomer_execute: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                externalApiId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ExternalApiRequestConfig"];
            };
        };
        responses: {
            /** @description The request has succeeded. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ExternalApiResponseSnapshot"];
                };
            };
            /** @description The server could not understand the request due to invalid syntax. */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ValidationError"];
                };
            };
            /** @description The server cannot find the requested resource. */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
            /** @description Server error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GenericError"];
                };
            };
        };
    };
}
type WithRequired<T, K extends keyof T> = T & {
    [P in K]-?: T[P];
};
